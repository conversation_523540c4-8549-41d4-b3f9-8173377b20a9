-- Add delivery_fee column to orders table
-- Run this script in your Supabase SQL editor

-- Add delivery_fee column to orders table if it doesn't exist
ALTER TABLE orders
ADD COLUMN IF NOT EXISTS delivery_fee DECIMAL(10, 2) DEFAULT 0;

-- Update existing orders to have 0 delivery fee (for historical data)
UPDATE orders 
SET delivery_fee = 0 
WHERE delivery_fee IS NULL;

-- Add a comment to the column for documentation
COMMENT ON COLUMN orders.delivery_fee IS 'Delivery fee charged for the order. 0 for pickup orders, positive amount for delivery orders.';
