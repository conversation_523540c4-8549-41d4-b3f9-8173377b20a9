-- Add delivery features to stores and create external delivery services table

-- 1. Add delivery fields to stores table
ALTER TABLE stores 
ADD COLUMN IF NOT EXISTS offers_delivery BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS delivery_fee DECIMAL(10, 2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS delivery_radius_km INTEGER DEFAULT 10,
ADD COLUMN IF NOT EXISTS delivery_time_estimate TEXT DEFAULT '1-2 hours';

-- Add comments for clarity
COMMENT ON COLUMN stores.offers_delivery IS 'Whether the store offers its own delivery service';
COMMENT ON COLUMN stores.delivery_fee IS 'Store delivery fee in local currency';
COMMENT ON COLUMN stores.delivery_radius_km IS 'Delivery radius in kilometers';
COMMENT ON COLUMN stores.delivery_time_estimate IS 'Estimated delivery time';

-- 2. Create external delivery services table
CREATE TABLE IF NOT EXISTS external_delivery_services (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  logo TEXT,
  contact_phone TEXT,
  contact_email TEXT,
  website TEXT,
  base_fee DECIMAL(10, 2) NOT NULL DEFAULT 0,
  per_km_fee DECIMAL(10, 2) DEFAULT 0,
  minimum_order_amount DECIMAL(10, 2) DEFAULT 0,
  maximum_delivery_radius_km INTEGER DEFAULT 50,
  estimated_delivery_time TEXT DEFAULT '30-60 minutes',
  is_active BOOLEAN DEFAULT true,
  coverage_areas TEXT[], -- Array of areas/regions they cover
  operating_hours JSONB, -- Store operating hours as JSON
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for external delivery services
CREATE INDEX IF NOT EXISTS external_delivery_services_active_idx ON external_delivery_services(is_active);
CREATE INDEX IF NOT EXISTS external_delivery_services_name_idx ON external_delivery_services(name);

-- Add comments
COMMENT ON TABLE external_delivery_services IS 'External delivery service providers';
COMMENT ON COLUMN external_delivery_services.base_fee IS 'Base delivery fee';
COMMENT ON COLUMN external_delivery_services.per_km_fee IS 'Additional fee per kilometer';
COMMENT ON COLUMN external_delivery_services.coverage_areas IS 'Array of areas/regions covered';
COMMENT ON COLUMN external_delivery_services.operating_hours IS 'Operating hours in JSON format';

-- 3. Create delivery service pricing table for dynamic pricing
CREATE TABLE IF NOT EXISTS delivery_service_pricing (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  service_id UUID NOT NULL REFERENCES external_delivery_services(id) ON DELETE CASCADE,
  area_name TEXT NOT NULL,
  base_fee DECIMAL(10, 2) NOT NULL,
  per_km_fee DECIMAL(10, 2) DEFAULT 0,
  minimum_order_amount DECIMAL(10, 2) DEFAULT 0,
  maximum_distance_km INTEGER DEFAULT 20,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(service_id, area_name)
);

-- Create indexes for delivery pricing
CREATE INDEX IF NOT EXISTS delivery_service_pricing_service_idx ON delivery_service_pricing(service_id);
CREATE INDEX IF NOT EXISTS delivery_service_pricing_area_idx ON delivery_service_pricing(area_name);
CREATE INDEX IF NOT EXISTS delivery_service_pricing_active_idx ON delivery_service_pricing(is_active);

-- Add comments
COMMENT ON TABLE delivery_service_pricing IS 'Area-specific pricing for delivery services';
COMMENT ON COLUMN delivery_service_pricing.area_name IS 'Name of the delivery area/region';

-- 4. Insert some sample external delivery services
INSERT INTO external_delivery_services (
  name, 
  description, 
  contact_phone, 
  contact_email,
  base_fee, 
  per_km_fee, 
  minimum_order_amount,
  maximum_delivery_radius_km,
  estimated_delivery_time,
  coverage_areas,
  operating_hours
) VALUES 
(
  'Quick Delivery Gambia',
  'Fast and reliable delivery service across The Gambia',
  '+************',
  '<EMAIL>',
  50.00,
  5.00,
  100.00,
  30,
  '30-45 minutes',
  ARRAY['Banjul', 'Serrekunda', 'Bakau', 'Fajara', 'Kololi'],
  '{"monday": "08:00-20:00", "tuesday": "08:00-20:00", "wednesday": "08:00-20:00", "thursday": "08:00-20:00", "friday": "08:00-20:00", "saturday": "09:00-18:00", "sunday": "10:00-16:00"}'::jsonb
),
(
  'Express Courier Services',
  'Professional courier and delivery solutions',
  '+************',
  '<EMAIL>',
  75.00,
  8.00,
  150.00,
  25,
  '45-60 minutes',
  ARRAY['Greater Banjul Area', 'Kanifing', 'Brikama'],
  '{"monday": "07:00-19:00", "tuesday": "07:00-19:00", "wednesday": "07:00-19:00", "thursday": "07:00-19:00", "friday": "07:00-19:00", "saturday": "08:00-17:00", "sunday": "closed"}'::jsonb
),
(
  'City Runners',
  'Same-day delivery within city limits',
  '+220 555 0123',
  '<EMAIL>',
  40.00,
  3.00,
  75.00,
  15,
  '20-30 minutes',
  ARRAY['Banjul', 'Serrekunda', 'Kanifing'],
  '{"monday": "09:00-21:00", "tuesday": "09:00-21:00", "wednesday": "09:00-21:00", "thursday": "09:00-21:00", "friday": "09:00-21:00", "saturday": "09:00-21:00", "sunday": "10:00-18:00"}'::jsonb
)
ON CONFLICT DO NOTHING;

-- 5. Insert sample pricing for different areas
INSERT INTO delivery_service_pricing (service_id, area_name, base_fee, per_km_fee, minimum_order_amount, maximum_distance_km)
SELECT 
  eds.id,
  area,
  CASE 
    WHEN area = 'Banjul' THEN 40.00
    WHEN area = 'Serrekunda' THEN 45.00
    WHEN area = 'Bakau' THEN 50.00
    ELSE 60.00
  END as base_fee,
  CASE 
    WHEN area = 'Banjul' THEN 3.00
    WHEN area = 'Serrekunda' THEN 4.00
    ELSE 5.00
  END as per_km_fee,
  100.00 as minimum_order_amount,
  20 as maximum_distance_km
FROM external_delivery_services eds,
UNNEST(eds.coverage_areas) as area
WHERE eds.name = 'Quick Delivery Gambia'
ON CONFLICT (service_id, area_name) DO NOTHING;

-- Show the results
SELECT 'DELIVERY SERVICES SETUP COMPLETE' as status;
SELECT * FROM external_delivery_services;
SELECT * FROM delivery_service_pricing;
