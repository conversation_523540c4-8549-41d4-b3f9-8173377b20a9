'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { createSPASassClient } from '@/lib/supabase/client';
import { EcommerceClientService } from '@/lib/services/ecommerce-client';
import { CartItem } from '@/lib/types/ecommerce';

interface CartContextType {
  cartItems: CartItem[];
  cartCount: number;
  loading: boolean;
  refreshCart: () => Promise<void>;
  addToCart: (productId: string | number, quantity: number) => Promise<void>;
  updateCartItem: (cartItemId: string, quantity: number) => Promise<void>;
  removeFromCart: (cartItemId: string) => Promise<void>;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export function CartProvider({ children }: { children: React.ReactNode }) {
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [userId, setUserId] = useState<string | null>(null);

  const fetchCart = async () => {
    try {
      setLoading(true);
      const supabase = await createSPASassClient();
      const { data: { user } } = await supabase.getSupabaseClient().auth.getUser();

      if (user) {
        setUserId(user.id);
        try {
          const items = await EcommerceClientService.getCart(user.id);
          setCartItems(items);
        } catch (error) {
          console.error('Error fetching cart items:', error);
          setCartItems([]);
        }
      } else {
        // Load guest cart from localStorage
        const localCart = JSON.parse(localStorage.getItem('guest_cart') || '[]');
        const guestItems = localCart.map((item: any) => ({
          id: `guest_${item.productId}`,
          user_id: 'guest',
          product_id: item.productId,
          quantity: item.quantity,
          options: null,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          product: null
        }));
        setCartItems(guestItems);
      }
    } catch (error) {
      console.error('Error fetching cart:', error);
      setCartItems([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCart();
  }, []);

  const refreshCart = async () => {
    await fetchCart();
  };

  const addToCart = async (productId: string | number, quantity: number) => {
    try {
      const supabase = await createSPASassClient();
      const { data: { user } } = await supabase.getSupabaseClient().auth.getUser();

      if (!user) {
        // Store in localStorage for unauthenticated users
        const localCart = JSON.parse(localStorage.getItem('guest_cart') || '[]');
        const existingItemIndex = localCart.findIndex((item: any) => item.productId === productId);

        if (existingItemIndex >= 0) {
          localCart[existingItemIndex].quantity += quantity;
        } else {
          localCart.push({ productId, quantity, addedAt: Date.now() });
        }

        localStorage.setItem('guest_cart', JSON.stringify(localCart));

        // Update local state to reflect the change
        setCartItems(prev => {
          const existingIndex = prev.findIndex(item => item.product_id === productId);
          if (existingIndex >= 0) {
            return prev.map((item, index) =>
              index === existingIndex
                ? { ...item, quantity: item.quantity + quantity }
                : item
            );
          } else {
            // For guest users, we'll need to fetch product details to display properly
            // For now, just add a placeholder that will be resolved when they sign in
            return [...prev, {
              id: `guest_${productId}`,
              user_id: 'guest',
              product_id: productId,
              quantity,
              options: null,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
              product: null // Will be resolved later
            } as any];
          }
        });
        return;
      }

      await EcommerceClientService.addToCart(productId, quantity);
      await refreshCart();
    } catch (error) {
      console.error('Error adding to cart:', error);
      throw error;
    }
  };

  const updateCartItem = async (cartItemId: string, quantity: number) => {
    try {
      await EcommerceClientService.updateCartItem(cartItemId, quantity);

      // Update local state immediately for better UX
      setCartItems(prevItems =>
        prevItems.map(item =>
          item.id === cartItemId ? { ...item, quantity } : item
        )
      );

      // Then refresh from server to ensure consistency
      await refreshCart();
    } catch (error) {
      console.error('Error updating cart item:', error);
      throw error;
    }
  };

  const removeFromCart = async (cartItemId: string) => {
    try {
      await EcommerceClientService.removeFromCart(cartItemId);

      // Update local state immediately for better UX
      setCartItems(prevItems => prevItems.filter(item => item.id !== cartItemId));

      // Then refresh from server to ensure consistency
      await refreshCart();
    } catch (error) {
      console.error('Error removing from cart:', error);
      throw error;
    }
  };

  return (
    <CartContext.Provider
      value={{
        cartItems,
        cartCount: cartItems.length,
        loading,
        refreshCart,
        addToCart,
        updateCartItem,
        removeFromCart,
      }}
    >
      {children}
    </CartContext.Provider>
  );
}

export const useCart = () => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};
