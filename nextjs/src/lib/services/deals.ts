import { Product } from '@/lib/types/ecommerce';
import { EcommerceClientService } from './ecommerce-client';
import { createSPASassClient } from '@/lib/supabase/client';

export interface DealProduct extends Product {
  discountPercentage: number;
  discountAmount: number;
  timeLeft?: string;
  dealType?: 'flash' | 'clearance' | 'seasonal' | 'regular';
  dealId?: string;
  dealTitle?: string;
  dealDescription?: string;
  dealStartDate?: string;
  dealEndDate?: string;
  dealMaxQuantity?: number;
  dealUsedQuantity?: number;
}

export interface DealsFilters {
  category?: string;
  minDiscount?: number;
  maxPrice?: number;
  dealType?: 'flash' | 'clearance' | 'seasonal' | 'all';
  sortBy?: 'discount' | 'price' | 'name' | 'newest';
  limit?: number;
}

export class DealsService {
  /**
   * Get all active deals from the deals table
   */
  static async getDeals(filters: DealsFilters = {}): Promise<DealProduct[]> {
    try {
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();

      // Build query to get active deals with product information
      let query = supabase
        .from('deals' as any)
        .select(`
          *,
          products!inner(
            id, name, slug, description, price, currency, category_id, store_id, featured, trending, in_stock, rating, review_count, created_at, updated_at
          )
        `)
        .eq('is_active', true)
        .lte('start_date', new Date().toISOString())
        .gte('end_date', new Date().toISOString());

      // Note: Category filtering will be done after fetching due to join complexity

      // Apply deal type filter
      if (filters.dealType && filters.dealType !== 'all') {
        query = query.eq('deal_type', filters.dealType);
      }

      // Apply limit
      if (filters.limit) {
        query = query.limit(filters.limit);
      }

      const { data: deals, error } = await query;

      if (error) {
        console.error('Error fetching deals from database:', error);
        // Fallback to old method
        return this.getFallbackDeals(filters);
      }

      if (!deals || deals.length === 0) {
        // Fallback to old method if no deals in database
        return this.getFallbackDeals(filters);
      }

      // Transform deals to DealProduct format
      const dealProducts = await Promise.all(
        deals.map(async (deal: any) => {
          const product = await this.enrichProductData(deal.products, supabase);
          return this.transformDealToProduct({ ...deal, products: product });
        })
      );

      // Apply filters
      const filteredProducts = dealProducts.filter(product => {
        // Apply minimum discount filter
        if (filters.minDiscount && product.discountPercentage < filters.minDiscount) {
          return false;
        }

        // Apply max price filter
        if (filters.maxPrice && product.price > filters.maxPrice) {
          return false;
        }

        // Apply category filter
        if (filters.category && product.category?.slug !== filters.category) {
          return false;
        }

        return true;
      });

      // Apply sorting
      return this.sortDeals(filteredProducts, filters.sortBy || 'discount');
    } catch (error) {
      console.error('Error fetching deals:', error);
      // Fallback to old method
      return this.getFallbackDeals(filters);
    }
  }

  /**
   * Fallback method using the old approach (compare_at_price vs price)
   */
  static async getFallbackDeals(filters: DealsFilters = {}): Promise<DealProduct[]> {
    try {
      // Get all products
      const allProducts = await EcommerceClientService.getProducts({
        limit: filters.limit || 100
      });

      // Filter products that have discounts
      const dealProducts = allProducts
        .filter(product => {
          const hasDiscount = product.compare_at_price && product.compare_at_price > product.price;
          if (!hasDiscount) return false;

          // Apply category filter
          if (filters.category && product.category?.slug !== filters.category) {
            return false;
          }

          // Apply max price filter
          if (filters.maxPrice && product.price > filters.maxPrice) {
            return false;
          }

          return true;
        })
        .map(product => this.transformToDealsProduct(product))
        .filter(product => {
          // Apply minimum discount filter
          if (filters.minDiscount && product.discountPercentage < filters.minDiscount) {
            return false;
          }

          // Apply deal type filter
          if (filters.dealType && filters.dealType !== 'all' && product.dealType !== filters.dealType) {
            return false;
          }

          return true;
        });

      // Apply sorting
      return this.sortDeals(dealProducts, filters.sortBy || 'discount');
    } catch (error) {
      console.error('Error fetching fallback deals:', error);
      return [];
    }
  }

  /**
   * Get flash deals (30%+ discount)
   */
  static async getFlashDeals(limit = 6): Promise<DealProduct[]> {
    return this.getDeals({
      minDiscount: 30,
      dealType: 'flash',
      sortBy: 'discount',
      limit
    });
  }

  /**
   * Get clearance deals (50%+ discount)
   */
  static async getClearanceDeals(limit = 6): Promise<DealProduct[]> {
    return this.getDeals({
      minDiscount: 50,
      dealType: 'clearance',
      sortBy: 'discount',
      limit
    });
  }

  /**
   * Get deals by category
   */
  static async getDealsByCategory(categorySlug: string, limit = 12): Promise<DealProduct[]> {
    return this.getDeals({
      category: categorySlug,
      sortBy: 'discount',
      limit
    });
  }

  /**
   * Get today's best deals (top discounts)
   */
  static async getTodaysDeals(limit = 8): Promise<DealProduct[]> {
    return this.getDeals({
      sortBy: 'discount',
      limit
    });
  }

  /**
   * Enrich product data with category, store, and images
   */
  private static async enrichProductData(product: any, supabase: any): Promise<any> {
    try {
      // Fetch category data
      let category = null;
      if (product.category_id) {
        const { data: categoryData } = await supabase
          .from('categories')
          .select('id, name, slug')
          .eq('id', product.category_id)
          .single();
        category = categoryData;
      }

      // Fetch store data
      let store = null;
      if (product.store_id) {
        const { data: storeData } = await supabase
          .from('stores')
          .select('id, name, slug')
          .eq('id', product.store_id)
          .single();
        store = storeData;
      }

      // Fetch product images
      const { data: images } = await supabase
        .from('product_images')
        .select('id, url, alt, position')
        .eq('product_id', product.id)
        .order('position');

      return {
        ...product,
        categories: category,
        stores: store,
        product_images: images || []
      };
    } catch (error) {
      console.error('Error enriching product data:', error);
      return {
        ...product,
        categories: null,
        stores: null,
        product_images: []
      };
    }
  }

  /**
   * Transform a deal from database to DealProduct format
   */
  private static transformDealToProduct(deal: any): DealProduct {
    const product = deal.products;
    const discountAmount = deal.original_price - deal.deal_price;
    const discountPercentage = deal.discount_percentage;

    // Calculate time left
    const endDate = new Date(deal.end_date);
    const now = new Date();
    const timeLeft = this.calculateTimeLeft(endDate);

    return {
      id: product.id,
      name: product.name,
      slug: product.slug,
      description: product.description,
      price: deal.deal_price, // Use deal price as the current price
      compare_at_price: deal.original_price, // Original price for comparison
      currency: deal.currency,
      category_id: product.category_id,
      categoryId: product.category_id,
      storeId: product.store_id,
      featured: product.featured,
      inventory_quantity: product.inventory_quantity,
      inStock: product.in_stock,
      rating: product.rating,
      image_url: product.product_images?.[0]?.url,
      imageUrl: product.product_images?.[0]?.url,
      category: product.categories ? {
        id: product.categories.id,
        name: product.categories.name,
        slug: product.categories.slug
      } : undefined,
      images: product.product_images?.map((img: any) => ({
        id: img.id,
        url: img.url,
        alt: img.alt,
        position: img.position
      })) || [],
      store: product.stores ? {
        id: product.stores.id,
        name: product.stores.name,
        slug: product.stores.slug
      } : undefined,
      created_at: product.created_at,
      createdAt: product.created_at,
      updatedAt: product.updated_at,
      review_count: product.review_count,
      // Deal-specific properties
      discountPercentage,
      discountAmount,
      dealType: deal.deal_type,
      timeLeft,
      dealId: deal.id,
      dealTitle: deal.title,
      dealDescription: deal.description,
      dealStartDate: deal.start_date,
      dealEndDate: deal.end_date,
      dealMaxQuantity: deal.max_quantity,
      dealUsedQuantity: deal.used_quantity
    };
  }

  /**
   * Calculate time left until deal expires
   */
  private static calculateTimeLeft(endDate: Date): string {
    const now = new Date();
    const diff = endDate.getTime() - now.getTime();

    if (diff <= 0) return 'Expired';

    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (days > 0) return `${days}d ${hours}h`;
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  }

  /**
   * Transform a regular product to a deal product (fallback method)
   */
  private static transformToDealsProduct(product: Product): DealProduct {
    const originalPrice = product.compare_at_price || product.price;
    const salePrice = product.price;

    const discountAmount = originalPrice - salePrice;
    const discountPercentage = Math.round((discountAmount / originalPrice) * 100);

    // Determine deal type based on discount percentage
    let dealType: DealProduct['dealType'] = 'regular';
    if (discountPercentage >= 50) {
      dealType = 'clearance';
    } else if (discountPercentage >= 30) {
      dealType = 'flash';
    } else if (discountPercentage >= 20) {
      dealType = 'seasonal';
    }

    return {
      ...product,
      discountPercentage,
      discountAmount,
      dealType,
      timeLeft: this.generateRandomTimeLeft()
    };
  }

  /**
   * Sort deals based on criteria
   */
  private static sortDeals(deals: DealProduct[], sortBy: string): DealProduct[] {
    return deals.sort((a, b) => {
      switch (sortBy) {
        case 'discount':
          return b.discountPercentage - a.discountPercentage;
        case 'price':
          return a.price - b.price;
        case 'name':
          return a.name.localeCompare(b.name);
        case 'newest':
          return new Date(b.created_at || '').getTime() - new Date(a.created_at || '').getTime();
        default:
          return 0;
      }
    });
  }

  /**
   * Generate random time left for deals (for display purposes)
   */
  private static generateRandomTimeLeft(): string {
    const hours = Math.floor(Math.random() * 48) + 1;
    const minutes = Math.floor(Math.random() * 60);
    return `${hours}h ${minutes}m`;
  }

  /**
   * Calculate savings for a deal
   */
  static calculateSavings(product: DealProduct): {
    amount: number;
    percentage: number;
    currency: string;
  } {
    return {
      amount: product.discountAmount,
      percentage: product.discountPercentage,
      currency: product.currency || 'GMD'
    };
  }

  /**
   * Check if a product is on sale
   */
  static isOnSale(product: Product): boolean {
    return !!(product.compare_at_price && product.compare_at_price > product.price);
  }

  /**
   * Get deal badge text based on discount percentage
   */
  static getDealBadge(discountPercentage: number): {
    text: string;
    color: string;
    bgColor: string;
  } {
    if (discountPercentage >= 50) {
      return {
        text: 'CLEARANCE',
        color: 'text-white',
        bgColor: 'bg-red-600'
      };
    } else if (discountPercentage >= 30) {
      return {
        text: 'FLASH SALE',
        color: 'text-white',
        bgColor: 'bg-orange-500'
      };
    } else if (discountPercentage >= 20) {
      return {
        text: 'SEASONAL',
        color: 'text-white',
        bgColor: 'bg-blue-500'
      };
    } else {
      return {
        text: 'SALE',
        color: 'text-white',
        bgColor: 'bg-green-500'
      };
    }
  }

  /**
   * Get deal urgency level
   */
  static getDealUrgency(discountPercentage: number): 'low' | 'medium' | 'high' {
    if (discountPercentage >= 50) return 'high';
    if (discountPercentage >= 30) return 'medium';
    return 'low';
  }
}

export default DealsService;
