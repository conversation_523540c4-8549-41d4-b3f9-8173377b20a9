"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Search,
  ShoppingCart,
  Heart,
  User,
  Menu,
  X,
  ChevronDown,
  LogOut,
  Bell
} from 'lucide-react';
import { createSPASassClient } from '@/lib/supabase/client';
import { Category } from '@/lib/types/ecommerce';
import { EcommerceClientService } from '@/lib/services/ecommerce-client';
import { useCart } from '@/lib/context/CartContext';
import { NotificationDropdown } from '@/components/notifications/NotificationDropdown';

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);
  const [isCategoriesDropdownOpen, setIsCategoriesDropdownOpen] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<{ email: string; id: string } | null>(null);
  const [userRole, setUserRole] = useState<string>('user');
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);

  // Use the cart context instead of local state
  const { cartCount } = useCart();

  const pathname = usePathname();

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const supabase = await createSPASassClient();
        const { data: { user } } = await supabase.getSupabaseClient().auth.getUser();

        console.log('Header auth check - user:', user ? { id: user.id, email: user.email } : null);

        setIsAuthenticated(!!user);
        if (user) {
          setUser({ email: user.email || '', id: user.id });

          // Get user role from profiles table
          try {
            const { data: profile, error: roleError } = await supabase.getSupabaseClient()
              .from('profiles')
              .select('role')
              .eq('id', user.id)
              .single();

            console.log('Header role check - profile:', profile, 'error:', roleError);

            if (profile?.role) {
              setUserRole(profile.role);
              console.log('Header - set user role to:', profile.role);
            } else {
              setUserRole('user');
              console.log('Header - defaulting to user role');
            }
          } catch (roleError) {
            console.log('Could not fetch user role:', roleError);
            // Default to 'user' role if we can't fetch it
            setUserRole('user');
          }
        } else {
          setUser(null);
          setUserRole('user');
        }
      } catch (error) {
        console.error('Error checking auth status:', error);
        setIsAuthenticated(false);
        setUser(null);
        setUserRole('user');
      } finally {
        setLoading(false);
      }
    };

    const fetchCategories = async () => {
      try {
        const data = await EcommerceClientService.getCategories();
        setCategories(data);
      } catch (error) {
        console.error('Error fetching categories:', error);
      }
    };

    checkAuth();
    fetchCategories();
  }, []);

  const handleLogout = async () => {
    try {
      const client = await createSPASassClient();
      await client.logout();
      setIsAuthenticated(false);
      setUser(null);
      setUserRole('user');
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  const getInitials = (email: string) => {
    const parts = email.split('@')[0].split(/[._-]/);
    return parts.length > 1
      ? (parts[0][0] + parts[1][0]).toUpperCase()
      : parts[0].slice(0, 2).toUpperCase();
  };

  const productName = process.env.NEXT_PUBLIC_PRODUCTNAME || "Finder";

  const navigation = [
    { name: 'Home', href: '/' },
    { name: 'Products', href: '/products' },
    { name: 'Stores', href: '/stores' },
    { name: 'Deals', href: '/deals' },
    { name: 'About', href: '/about' },
    { name: 'Contact', href: '/contact' },
  ];

  return (
    <header className="relative bg-white shadow-sm">
      {/* Top bar */}
      <div className="bg-primary-600 py-2 text-center text-sm text-white">
        <p>Free delivery within The Gambia. Shop with confidence!</p>
      </div>

      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          {/* Mobile menu button */}
          <div className="flex lg:hidden">
            <button
              type="button"
              className="inline-flex items-center justify-center rounded-md p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
              onClick={() => setIsMenuOpen(true)}
            >
              <span className="sr-only">Open main menu</span>
              <Menu className="h-6 w-6" aria-hidden="true" />
            </button>
          </div>

          {/* Logo */}
          <div className="flex flex-shrink-0 items-center">
            <Link href="/" className="text-2xl font-bold text-primary-600">
              {productName}
            </Link>
          </div>

          {/* Desktop navigation */}
          <nav className="hidden lg:flex lg:space-x-8">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className={`inline-flex items-center px-1 py-2 text-sm font-medium ${
                  pathname === item.href
                    ? 'text-primary-600'
                    : 'text-gray-500 hover:text-gray-900'
                }`}
              >
                {item.name}
              </Link>
            ))}

            {/* Categories dropdown */}
            <div className="relative">
              <button
                type="button"
                className="inline-flex items-center px-1 py-2 text-sm font-medium text-gray-500 hover:text-gray-900"
                onClick={() => setIsCategoriesDropdownOpen(!isCategoriesDropdownOpen)}
              >
                Categories
                <ChevronDown className="ml-1 h-4 w-4" />
              </button>

              {isCategoriesDropdownOpen && (
                <div className="absolute left-0 z-10 mt-2 w-64 origin-top-left rounded-md bg-white py-2 shadow-lg ring-1 ring-black ring-opacity-5 max-h-96 overflow-y-auto">
                  <div className="grid grid-cols-2 gap-1 px-2">
                    {categories.slice(0, 12).map((category) => (
                      <Link
                        key={category.id}
                        href={`/categories/${category.slug}`}
                        className="block px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md truncate"
                        onClick={() => setIsCategoriesDropdownOpen(false)}
                        title={category.name}
                      >
                        {category.name}
                      </Link>
                    ))}
                  </div>
                  {categories.length > 12 && (
                    <div className="border-t border-gray-100 mt-2 pt-2">
                      <Link
                        href="/categories"
                        className="block px-4 py-2 text-sm text-primary-600 hover:bg-primary-50 font-medium text-center"
                        onClick={() => setIsCategoriesDropdownOpen(false)}
                      >
                        View All Categories ({categories.length})
                      </Link>
                    </div>
                  )}
                </div>
              )}
            </div>
          </nav>

          {/* Right section */}
          <div className="flex items-center space-x-4">
            {/* Find */}
            <Link
              href="/find"
              className="p-2 text-gray-400 hover:text-gray-500"
            >
              <span className="sr-only">Find</span>
              <Search className="h-6 w-6" />
            </Link>

            {/* Wishlist */}
            <Link
              href="/wishlist"
              className="p-2 text-gray-400 hover:text-gray-500"
            >
              <span className="sr-only">Wishlist</span>
              <Heart className="h-6 w-6" />
            </Link>

            {/* Cart */}
            <Link
              href="/cart"
              className="relative p-2 text-gray-400 hover:text-gray-500"
            >
              <span className="sr-only">Cart</span>
              <ShoppingCart className="h-6 w-6" />
              {cartCount > 0 && (
                <span className="absolute -right-1 -top-1 flex h-5 w-5 items-center justify-center rounded-full bg-primary-600 text-xs font-bold text-white">
                  {cartCount}
                </span>
              )}
            </Link>

            {/* Notifications - only show for authenticated users */}
            {!loading && isAuthenticated && (
              <NotificationDropdown />
            )}

            {/* User account */}
            {!loading && (
              isAuthenticated ? (
                <div className="relative">
                  <button
                    type="button"
                    className="flex items-center space-x-1 rounded-full text-sm text-gray-700 hover:text-gray-900"
                    onClick={() => setIsUserDropdownOpen(!isUserDropdownOpen)}
                  >
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary-100 text-primary-700">
                      {user?.email ? getInitials(user.email) : '?'}
                    </div>
                  </button>

                  {isUserDropdownOpen && (
                    <div className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5">
                      <div className="border-b border-gray-100 px-4 py-2">
                        <p className="text-xs text-gray-500">Signed in as</p>
                        <p className="truncate text-sm font-medium text-gray-900">
                          {user?.email}
                        </p>
                      </div>

                      <Link
                        href="/account"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => setIsUserDropdownOpen(false)}
                      >
                        My Account
                      </Link>
                      <Link
                        href="/my-orders"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => setIsUserDropdownOpen(false)}
                      >
                        My Orders
                      </Link>
                      <Link
                        href="/notifications"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => setIsUserDropdownOpen(false)}
                      >
                        Notifications
                      </Link>

                      {/* Admin access based on role */}
                      {user && (userRole === 'admin' || userRole === 'store_owner') && (
                        <Link
                          href="/admin"
                          className="block px-4 py-2 text-sm text-blue-600 hover:bg-blue-50 font-medium"
                          onClick={() => setIsUserDropdownOpen(false)}
                        >
                          🛡️ Admin Panel
                        </Link>
                      )}

                      {/* Debug info - remove in production */}
                      {user && (
                        <div className="px-4 py-2 text-xs text-gray-500 border-t">
                          Role: {userRole} | Email: {user.email}
                        </div>
                      )}

                      <button
                        type="button"
                        className="block w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50"
                        onClick={() => {
                          handleLogout();
                          setIsUserDropdownOpen(false);
                        }}
                      >
                        Sign Out
                      </button>
                    </div>
                  )}
                </div>
              ) : (
                <Link
                  href="/auth/login"
                  className="flex items-center space-x-1 rounded-md bg-primary-600 px-4 py-2 text-sm font-medium text-white hover:bg-primary-700"
                >
                  <User className="h-4 w-4" />
                  <span>Sign In</span>
                </Link>
              )
            )}
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {isMenuOpen && (
        <div className="fixed inset-0 z-40 lg:hidden">
          {/* Overlay */}
          <div
            className="fixed inset-0 bg-black bg-opacity-25"
            onClick={() => setIsMenuOpen(false)}
          ></div>

          {/* Menu panel */}
          <div className="fixed inset-y-0 left-0 z-40 w-full max-w-xs overflow-y-auto bg-white pb-12 shadow-xl">
            <div className="flex items-center justify-between px-4 pt-5">
              <button
                type="button"
                className="inline-flex items-center justify-center rounded-md p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
                onClick={() => setIsMenuOpen(false)}
              >
                <span className="sr-only">Close menu</span>
                <X className="h-6 w-6" aria-hidden="true" />
              </button>
            </div>

            {/* Links */}
            <div className="mt-2 space-y-2 px-4">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="block rounded-md px-3 py-2 text-base font-medium text-gray-900 hover:bg-gray-100"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}

              {/* Categories */}
              <div className="py-2">
                <h3 className="px-3 text-sm font-medium text-gray-500">Categories</h3>
                <div className="mt-2 space-y-1">
                  {categories.map((category) => (
                    <Link
                      key={category.id}
                      href={`/categories/${category.slug}`}
                      className="block rounded-md px-3 py-2 text-base font-medium text-gray-900 hover:bg-gray-100"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      {category.name}
                    </Link>
                  ))}
                </div>
              </div>

              {/* Account links */}
              {isAuthenticated ? (
                <div className="border-t border-gray-200 pt-4">
                  <div className="flex items-center px-3 py-2">
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary-100 text-primary-700">
                      {user?.email ? getInitials(user.email) : '?'}
                    </div>
                    <span className="ml-3 truncate text-sm font-medium text-gray-900">
                      {user?.email}
                    </span>
                  </div>
                  <div className="mt-2 space-y-1">

                    <Link
                      href="/account"
                      className="block rounded-md px-3 py-2 text-base font-medium text-gray-900 hover:bg-gray-100"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      My Account
                    </Link>
                    <Link
                      href="/my-orders"
                      className="block rounded-md px-3 py-2 text-base font-medium text-gray-900 hover:bg-gray-100"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      My Orders
                    </Link>
                    <Link
                      href="/notifications"
                      className="block rounded-md px-3 py-2 text-base font-medium text-gray-900 hover:bg-gray-100"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Notifications
                    </Link>

                    {/* Admin access for mobile */}
                    {(userRole === 'admin' || userRole === 'store_owner') && (
                      <Link
                        href="/admin"
                        className="block rounded-md px-3 py-2 text-base font-medium text-blue-600 hover:bg-blue-50"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        🛡️ Admin Panel
                      </Link>
                    )}

                    <button
                      type="button"
                      className="block w-full rounded-md px-3 py-2 text-left text-base font-medium text-red-600 hover:bg-red-50"
                      onClick={() => {
                        handleLogout();
                        setIsMenuOpen(false);
                      }}
                    >
                      Sign Out
                    </button>
                  </div>
                </div>
              ) : (
                <div className="border-t border-gray-200 pt-4">
                  <Link
                    href="/auth/login"
                    className="block rounded-md px-3 py-2 text-base font-medium text-gray-900 hover:bg-gray-100"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Sign In
                  </Link>
                  <Link
                    href="/auth/register"
                    className="block rounded-md px-3 py-2 text-base font-medium text-gray-900 hover:bg-gray-100"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Create Account
                  </Link>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </header>
  );
}
