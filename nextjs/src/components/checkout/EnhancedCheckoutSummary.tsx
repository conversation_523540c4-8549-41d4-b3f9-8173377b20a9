import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { formatCurrency } from '@/lib/utils';
import { CartItem } from '@/lib/types/ecommerce';
import { CheckoutService } from '@/lib/services/checkout';

interface EnhancedCheckoutSummaryProps {
  cartItems: CartItem[];
  currency: string;
  deliveryMethod: 'delivery' | 'pickup';
  className?: string;
}

const EnhancedCheckoutSummary: React.FC<EnhancedCheckoutSummaryProps> = ({
  cartItems,
  currency,
  deliveryMethod,
  className = ''
}) => {
  // Calculate subtotal
  const subtotal = cartItems.reduce((sum, item) => {
    const price = item.product?.price || 0;
    return sum + (price * item.quantity);
  }, 0);

  // Calculate service fee
  const serviceFee = CheckoutService.calculateServiceFee(subtotal);

  // Calculate delivery fee
  const deliveryFee = CheckoutService.calculateDeliveryFee(deliveryMethod);

  // Calculate total
  const total = subtotal + serviceFee + deliveryFee;

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-lg font-semibold">Order Summary</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Cart Items */}
        <div className="space-y-3">
          {cartItems.map((item) => (
            <div key={item.id} className="flex justify-between items-start">
              <div className="flex-1">
                <h4 className="font-medium text-sm">{item.product?.name}</h4>
                <p className="text-xs text-gray-500">Qty: {item.quantity}</p>
              </div>
              <div className="text-sm font-medium">
                {formatCurrency((item.product?.price || 0) * item.quantity, currency)}
              </div>
            </div>
          ))}
        </div>

        <Separator />

        {/* Pricing Breakdown */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Subtotal</span>
            <span>{formatCurrency(subtotal, currency)}</span>
          </div>

          <div className="flex justify-between text-sm">
            <span>Service Fee</span>
            <span>{formatCurrency(serviceFee, currency)}</span>
          </div>

          <div className="flex justify-between text-sm">
            <span>Delivery ({deliveryMethod === 'delivery' ? 'Home Delivery' : 'Store Pickup'})</span>
            <span>{deliveryFee > 0 ? formatCurrency(deliveryFee, currency) : 'Free'}</span>
          </div>
        </div>

        <Separator />

        {/* Total */}
        <div className="flex justify-between items-center text-lg font-semibold">
          <span>Total</span>
          <span className="text-primary-600">{formatCurrency(total, currency)}</span>
        </div>

        {/* Service Fee Info */}
        <div className="text-xs text-gray-500 bg-gray-50 p-3 rounded-lg">
          <p className="font-medium mb-1">Service Fee Information:</p>
          <p>
            • Products over 201 {currency}: 5 {currency} service fee<br/>
            • Products 201 {currency} and below: 2 {currency} service fee
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default EnhancedCheckoutSummary;
