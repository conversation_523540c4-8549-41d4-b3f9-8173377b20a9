import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Truck, MapPin } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';
import { CheckoutService } from '@/lib/services/checkout';

interface DeliveryMethodSelectorProps {
  selectedMethod: 'delivery' | 'pickup';
  onMethodChange: (method: 'delivery' | 'pickup') => void;
  className?: string;
}

const DeliveryMethodSelector: React.FC<DeliveryMethodSelectorProps> = ({
  selectedMethod,
  onMethodChange,
  className = ''
}) => {
  const deliveryFee = CheckoutService.calculateDeliveryFee('delivery');
  const pickupFee = CheckoutService.calculateDeliveryFee('pickup');

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-lg font-semibold">Delivery Method</CardTitle>
      </CardHeader>
      <CardContent>
        <RadioGroup
          value={selectedMethod}
          onValueChange={(value) => onMethodChange(value as 'delivery' | 'pickup')}
          className="space-y-4"
        >
          <div className="flex items-center space-x-3 p-4 border rounded-lg hover:bg-gray-50 transition-colors">
            <RadioGroupItem value="delivery" id="delivery" />
            <div className="flex items-center space-x-3 flex-1">
              <Truck className="h-5 w-5 text-primary-600" />
              <div className="flex-1">
                <Label htmlFor="delivery" className="text-base font-medium cursor-pointer">
                  Home Delivery
                </Label>
                <p className="text-sm text-gray-500">
                  Get your order delivered to your doorstep
                </p>
              </div>
              <div className="text-right">
                <span className="text-base font-medium text-primary-600">
                  {formatCurrency(deliveryFee, 'GMD')}
                </span>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-3 p-4 border rounded-lg hover:bg-gray-50 transition-colors">
            <RadioGroupItem value="pickup" id="pickup" />
            <div className="flex items-center space-x-3 flex-1">
              <MapPin className="h-5 w-5 text-primary-600" />
              <div className="flex-1">
                <Label htmlFor="pickup" className="text-base font-medium cursor-pointer">
                  Store Pickup
                </Label>
                <p className="text-sm text-gray-500">
                  Pick up your order from the store location
                </p>
              </div>
              <div className="text-right">
                <span className="text-base font-medium text-green-600">
                  {pickupFee === 0 ? 'Free' : formatCurrency(pickupFee, 'GMD')}
                </span>
              </div>
            </div>
          </div>
        </RadioGroup>

        {selectedMethod === 'pickup' && (
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-800">
              <strong>Note:</strong> You will receive store location details and pickup instructions after your order is confirmed.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default DeliveryMethodSelector;
