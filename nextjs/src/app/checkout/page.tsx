'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useToast } from '@/lib/hooks/use-toast';
import { createSPASassClient } from '@/lib/supabase/client';
import { CheckoutService } from '@/lib/services/checkout';
import { useCart } from '@/lib/context/CartContext';
import EcommerceLayout from '@/components/ecommerce/EcommerceLayout';
import EnhancedCheckoutSummary from '@/components/checkout/EnhancedCheckoutSummary';
import DeliveryMethodSelector from '@/components/checkout/DeliveryMethodSelector';
import AddressConfirmation from '@/components/checkout/AddressConfirmation';
import ShippingAddressForm from '@/components/checkout/ShippingAddressForm';
import PaymentMethodSelector from '@/components/checkout/PaymentMethodSelector';
import WavePayment from '@/components/checkout/WavePayment';
import CryptoPayment from '@/components/checkout/CryptoPayment';
import TransactionVerification from '@/components/checkout/TransactionVerification';
import OrderStatusIndicator from '@/components/checkout/OrderStatusIndicator';
import { Button } from '@/components/ui/button';
import { ShoppingBag, ArrowLeft } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';
import Link from 'next/link';

type CheckoutStep = 'summary' | 'delivery' | 'address_confirmation' | 'shipping' | 'payment_method' | 'payment' | 'verification' | 'confirmation';
type PaymentMethod = 'wave' | 'crypto';

interface ShippingAddress {
  name: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  country: string;
  postalCode: string;
}

export default function CheckoutPage() {
  const { cartItems, loading } = useCart();
  const [userId, setUserId] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState<CheckoutStep>('summary');
  const [deliveryMethod, setDeliveryMethod] = useState<'delivery' | 'pickup'>('delivery');
  const [shippingAddress, setShippingAddress] = useState<ShippingAddress | null>(null);
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod | null>(null);
  const [orderId, setOrderId] = useState<string | null>(null);
  const [orderStatus, setOrderStatus] = useState<string>('pending');
  const [inventoryChecked, setInventoryChecked] = useState(false);
  const [inventoryError, setInventoryError] = useState<string | null>(null);
  const { toast } = useToast();
  const router = useRouter();

  // Calculate cart totals
  const subtotal = cartItems.reduce((total, item) => {
    return total + (item.product?.price || 0) * item.quantity;
  }, 0);

  const currency = 'GMD'; // Fixed currency
  const serviceFee = CheckoutService.calculateServiceFee(subtotal);
  const deliveryFee = CheckoutService.calculateDeliveryFee(deliveryMethod);
  const total = subtotal + serviceFee + deliveryFee;

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const supabase = await createSPASassClient();
        const { data: { user } } = await supabase.getSupabaseClient().auth.getUser();

        if (user) {
          setUserId(user.id);
        } else {
          // Redirect to login if not authenticated
          router.push('/auth/login?redirect=/checkout');
        }
      } catch (error) {
        console.error('Error checking auth:', error);
        toast({
          title: 'Error',
          description: 'Failed to verify your account. Please try again.',
          variant: 'destructive',
        });
      }
    };

    checkAuth();
  }, [toast, router]);

  // Check inventory when cart items change
  useEffect(() => {
    const checkInventory = async () => {
      if (cartItems.length > 0) {
        try {
          const { available, outOfStockItems } = await CheckoutService.checkInventoryAvailability(cartItems);
          setInventoryChecked(true);

          if (!available) {
            setInventoryError(`The following items are out of stock: ${outOfStockItems.join(', ')}`);
          } else {
            setInventoryError(null);
          }
        } catch (error) {
          console.error('Error checking inventory:', error);
          setInventoryError('Unable to verify inventory. Please try again.');
        }
      }
    };

    checkInventory();
  }, [cartItems]);

  const handleSelectPaymentMethod = (method: PaymentMethod) => {
    setPaymentMethod(method);
    setCurrentStep('payment');
  };

  const handleBackToSummary = () => {
    setCurrentStep('summary');
  };

  const handleBackToPaymentMethod = () => {
    setCurrentStep('payment_method');
    setPaymentMethod(null);
  };

  const handleProceedToDelivery = () => {
    setCurrentStep('delivery');
  };

  const handleDeliveryMethodSubmit = (method: 'delivery' | 'pickup') => {
    setDeliveryMethod(method);
    setCurrentStep('address_confirmation');
  };

  const handleAddressConfirmation = (address?: ShippingAddress) => {
    if (deliveryMethod === 'pickup') {
      // For pickup, no address needed, go directly to payment
      setCurrentStep('payment_method');
    } else if (address) {
      // For delivery with confirmed address, go to payment
      setShippingAddress(address);
      setCurrentStep('payment_method');
    } else {
      // For delivery without address, go to shipping form
      setCurrentStep('shipping');
    }
  };

  const handleEditAddress = () => {
    setCurrentStep('shipping');
  };

  const handleShippingSubmit = (address: ShippingAddress) => {
    setShippingAddress(address);
    setCurrentStep('payment_method');
  };

  const handleBackToAddressConfirmation = () => {
    setCurrentStep('address_confirmation');
  };

  const handleProceedToVerification = () => {
    setCurrentStep('verification');
  };

  const handleVerifyPayment = async (transactionId: string) => {
    try {
      if (!userId) {
        throw new Error('User not authenticated');
      }

      // Check inventory before creating order
      const { available, outOfStockItems } = await CheckoutService.checkInventoryAvailability(cartItems);
      if (!available) {
        throw new Error(`The following items are out of stock: ${outOfStockItems.join(', ')}`);
      }

      // Create the order with shipping address
      const order = await CheckoutService.createOrder({
        userId,
        cartItems,
        subtotal,
        serviceFee,
        deliveryFee,
        total,
        currency,
        deliveryMethod,
        shippingAddress: shippingAddress ? {
          name: shippingAddress.name,
          email: shippingAddress.email,
          phone: shippingAddress.phone,
          address: shippingAddress.address,
          city: shippingAddress.city,
          state: shippingAddress.state,
          country: shippingAddress.country,
          postalCode: shippingAddress.postalCode,
        } : undefined
      });

      // Create the payment
      await CheckoutService.createPayment({
        orderId: order.id,
        amount: total,
        currency,
        paymentMethod: paymentMethod || 'unknown',
        transactionId
      });

      setOrderId(order.id);
      setOrderStatus(order.status);
      setCurrentStep('confirmation');
    } catch (error) {
      console.error('Error processing payment:', error);
      toast({
        title: 'Error',
        description: 'Failed to process your payment. Please try again.',
        variant: 'destructive',
      });
    }
  };

  return (
    <EcommerceLayout>
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-8">Checkout</h1>

        {loading ? (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
          </div>
        ) : cartItems.length === 0 ? (
          <div className="text-center py-12">
            <ShoppingBag className="mx-auto h-16 w-16 text-gray-400 mb-4" />
            <h2 className="text-2xl font-semibold mb-2">Your cart is empty</h2>
            <p className="text-gray-600 mb-6">Looks like you haven't added any products to your cart yet.</p>
            <Link href="/products">
              <Button className="bg-primary-600 hover:bg-primary-700">
                Continue Shopping
              </Button>
            </Link>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2">
              {/* Step Navigation */}
              <div className="mb-6">
                {currentStep !== 'summary' && (
                  <Button
                    variant="ghost"
                    className="flex items-center text-gray-600"
                    onClick={
                      currentStep === 'delivery'
                        ? handleBackToSummary
                        : currentStep === 'address_confirmation'
                        ? () => setCurrentStep('delivery')
                        : currentStep === 'shipping'
                        ? handleBackToAddressConfirmation
                        : currentStep === 'payment_method'
                          ? deliveryMethod === 'pickup'
                            ? () => setCurrentStep('address_confirmation')
                            : shippingAddress
                              ? () => setCurrentStep('address_confirmation')
                              : () => setCurrentStep('shipping')
                          : currentStep === 'payment'
                            ? handleBackToPaymentMethod
                            : currentStep === 'verification'
                              ? () => setCurrentStep('payment')
                              : undefined
                    }
                  >
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Back
                  </Button>
                )}
              </div>

              {/* Inventory Error Display */}
              {inventoryError && (
                <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-red-800 font-medium">Inventory Issue</p>
                  <p className="text-red-600 text-sm">{inventoryError}</p>
                </div>
              )}

              {/* Current Step Content */}
              {currentStep === 'summary' && (
                <div className="space-y-6">
                  <EnhancedCheckoutSummary
                    cartItems={cartItems}
                    currency={currency}
                    deliveryMethod={deliveryMethod}
                  />
                  <div className="flex justify-end">
                    <Button
                      onClick={handleProceedToDelivery}
                      disabled={inventoryError !== null}
                      className="bg-primary-600 hover:bg-primary-700"
                    >
                      Proceed to Delivery Options
                    </Button>
                  </div>
                </div>
              )}

              {currentStep === 'delivery' && (
                <DeliveryMethodSelector
                  selectedMethod={deliveryMethod}
                  onMethodChange={handleDeliveryMethodSubmit}
                />
              )}

              {currentStep === 'address_confirmation' && (
                <AddressConfirmation
                  deliveryMethod={deliveryMethod}
                  onConfirm={handleAddressConfirmation}
                  onEditAddress={handleEditAddress}
                  onBack={() => setCurrentStep('delivery')}
                />
              )}

              {currentStep === 'shipping' && (
                <ShippingAddressForm
                  onSubmit={handleShippingSubmit}
                  onBack={handleBackToAddressConfirmation}
                />
              )}

              {currentStep === 'payment_method' && (
                <PaymentMethodSelector onSelect={handleSelectPaymentMethod} />
              )}

              {currentStep === 'payment' && paymentMethod === 'wave' && (
                <WavePayment
                  amount={total}
                  currency={currency}
                  onProceed={handleProceedToVerification}
                />
              )}

              {currentStep === 'payment' && paymentMethod === 'crypto' && (
                <CryptoPayment
                  amount={total}
                  currency={currency}
                  onProceed={handleProceedToVerification}
                />
              )}

              {currentStep === 'verification' && (
                <TransactionVerification
                  paymentMethod={paymentMethod!}
                  onVerify={handleVerifyPayment}
                />
              )}

              {currentStep === 'confirmation' && orderId && (
                <div className="bg-white p-6 rounded-lg shadow-sm">
                  <OrderStatusIndicator status={orderStatus} />
                  <div className="text-center mt-6">
                    <h2 className="text-2xl font-semibold mb-2">Thank you for your order!</h2>
                    <p className="text-gray-600 mb-6">
                      Your order #{orderId.substring(0, 8)} has been received and is being processed.
                    </p>
                    <Link href="/products">
                      <Button className="bg-primary-600 hover:bg-primary-700">
                        Continue Shopping
                      </Button>
                    </Link>
                  </div>
                </div>
              )}
            </div>

            {/* Order Summary Sidebar */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-sm overflow-hidden sticky top-24">
                <div className="p-6">
                  <h2 className="text-xl font-semibold mb-4">Order Summary</h2>

                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Subtotal</span>
                      <span className="font-medium">{formatCurrency(subtotal, currency)}</span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-gray-600">Service Fee</span>
                      <span className="font-medium">{formatCurrency(serviceFee, currency)}</span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-gray-600">Delivery ({deliveryMethod === 'delivery' ? 'Home Delivery' : 'Store Pickup'})</span>
                      <span className="font-medium">{deliveryFee > 0 ? formatCurrency(deliveryFee, currency) : 'Free'}</span>
                    </div>

                    <div className="border-t border-gray-200 pt-4">
                      <div className="flex justify-between font-semibold">
                        <span>Total</span>
                        <span className="text-primary-600">{formatCurrency(total, currency)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </EcommerceLayout>
  );
}
