import React from 'react';
import EcommerceLayout from '@/components/ecommerce/EcommerceLayout';
import Hero from '@/components/ecommerce/Hero';
import Newsletter from '@/components/ecommerce/Newsletter';
import Deals from '@/components/ecommerce/Deals';

// Import our new feature components
import { FeaturedProducts } from '@/features/products/components';
import { FeaturedCategories } from '@/features/categories/components';
import { FeaturedStores } from '@/features/stores/components';

export default function Home() {
  return (
    <EcommerceLayout>
      {/* Hero Section */}
      <Hero />

      {/* Quick Access Categories */}
      <FeaturedCategories
        title="Shop by Category"
        subtitle="Find exactly what you&apos;re looking for"
        variant="featured"
        limit={6}
      />

      {/* Featured Products */}
      <FeaturedProducts
        title="Featured Products"
        subtitle="Handpicked items just for you"
        limit={8}
      />

      {/* Popular Stores */}
      <FeaturedStores
        title="Popular Stores"
        subtitle="Discover trusted sellers in our marketplace"
        variant="featured"
        limit={6}
      />

      {/* Trending Products */}
      <FeaturedProducts
        title="Trending Now"
        subtitle="What&apos;s hot in The Gambia right now"
        limit={6}
      />

      {/* Deals Section */}
      <Deals
        title="Today&apos;s Best Deals"
        subtitle="Limited time offers you don&apos;t want to miss"
      />

      {/* New Arrivals */}
      <FeaturedProducts
        title="New Arrivals"
        subtitle="Fresh products added to our marketplace"
        limit={4}
      />

      {/* Newsletter */}
      <Newsletter />
    </EcommerceLayout>
  );
}