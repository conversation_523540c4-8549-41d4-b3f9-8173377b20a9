"use client";
import React, { useState, useEffect } from 'react';
import { AdminHeader, DataTable, StatusBadge } from '@/components/admin';
import { getCategories, deleteCategory } from '@/features/admin/api';
import { Category, CategoryListParams } from '@/features/admin/types';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Edit, Eye, Star, Trash2, Tag, Plus } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import Image from 'next/image';

export default function CategoriesPage() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [parentCategories, setParentCategories] = useState<Category[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [deleting, setDeleting] = useState<string | null>(null);
  const [params, setParams] = useState<CategoryListParams>({
    page: 1,
    per_page: 10,
    sort_by: 'name',
    sort_order: 'asc',
  });

  useEffect(() => {
    const fetchCategories = async () => {
      setLoading(true);
      try {
        const { categories, count } = await getCategories(params);
        setCategories(categories);
        setTotalCount(count);
      } catch (error) {
        console.error('Error fetching categories:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, [params]);

  useEffect(() => {
    const fetchParentCategories = async () => {
      try {
        const { categories } = await getCategories({ parent_id: null });
        setParentCategories(categories);
      } catch (error) {
        console.error('Error fetching parent categories:', error);
      }
    };

    fetchParentCategories();
  }, []);

  const handlePageChange = (page: number) => {
    setParams({ ...params, page });
  };

  const handleSearch = (search: string) => {
    setParams({ ...params, search, page: 1 });
  };

  const handleFeaturedFilter = (featured: string) => {
    setParams({
      ...params,
      featured: featured === 'all' ? undefined : featured === 'true',
      page: 1,
    });
  };

  const handleParentFilter = (parentId: string) => {
    setParams({
      ...params,
      parent_id: parentId === 'all' ? undefined : parentId === 'none' ? null : parentId,
      page: 1,
    });
  };

  const handleDelete = async (categoryId: string) => {
    setDeleting(categoryId);
    try {
      const success = await deleteCategory(categoryId);
      if (success) {
        // Refresh the categories list
        const { categories, count } = await getCategories(params);
        setCategories(categories);
        setTotalCount(count);
      } else {
        alert('Failed to delete category. It may have associated products.');
      }
    } catch (error) {
      console.error('Error deleting category:', error);
      alert('Failed to delete category');
    } finally {
      setDeleting(null);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Table columns
  const columns = [
    {
      key: 'name',
      header: 'Category',
      cell: (category: Category) => (
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden">
            {category.thumbnail || category.image ? (
              <Image
                src={category.thumbnail || category.image || ''}
                alt={category.name}
                width={40}
                height={40}
                className="w-full h-full object-cover"
              />
            ) : (
              <Tag className="h-5 w-5 text-gray-500" />
            )}
          </div>
          <div>
            <div className="flex items-center gap-2">
              {category.parent_id && (
                <span className="text-gray-400">└─</span>
              )}
              <span className={`font-medium ${category.parent_id ? 'text-gray-700' : 'text-gray-900'}`}>
                {category.name}
              </span>
            </div>
            <div className="text-sm text-gray-500">
              {category.parent && (
                <span className="text-blue-600">{category.parent.name} → </span>
              )}
              /{category.slug}
            </div>
          </div>
        </div>
      ),
    },
    {
      key: 'description',
      header: 'Description',
      cell: (category: Category) => (
        <div className="max-w-xs">
          <p className="text-sm text-gray-600 truncate">
            {category.description || 'No description'}
          </p>
        </div>
      ),
    },
    {
      key: 'product_count',
      header: 'Products',
      cell: (category: Category) => (
        <div className="text-center">
          <span className="font-medium">{category.product_count || 0}</span>
        </div>
      ),
    },
    {
      key: 'status',
      header: 'Status',
      cell: (category: Category) => (
        <div className="flex items-center gap-2">
          <Badge variant="secondary">Active</Badge>
          {category.featured && (
            <Badge variant="default" className="flex items-center gap-1">
              <Star className="h-3 w-3" />
              Featured
            </Badge>
          )}
        </div>
      ),
    },
    {
      key: 'created_at',
      header: 'Created',
      cell: (category: Category) => (
        <div className="text-sm text-gray-500">
          {formatDate(category.created_at)}
        </div>
      ),
    },
    {
      key: 'actions',
      header: 'Actions',
      cell: (category: Category) => (
        <div className="flex items-center gap-2">
          <Link href={`/admin/categories/${category.id}`}>
            <Button variant="ghost" size="sm">
              <Eye className="h-4 w-4" />
            </Button>
          </Link>
          <Link href={`/admin/categories/${category.id}/edit`}>
            <Button variant="ghost" size="sm">
              <Edit className="h-4 w-4" />
            </Button>
          </Link>
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700">
                <Trash2 className="h-4 w-4" />
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Delete Category</AlertDialogTitle>
                <AlertDialogDescription>
                  Are you sure you want to delete "{category.name}"? This action cannot be undone.
                  Categories with products cannot be deleted.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction
                  onClick={() => handleDelete(category.id)}
                  disabled={deleting === category.id}
                  className="bg-red-600 hover:bg-red-700"
                >
                  {deleting === category.id ? 'Deleting...' : 'Delete'}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <AdminHeader
        title="Categories"
        description="Manage product categories"
        actionHref="/admin/categories/new"
        actionLabel="Add Category"
      />

      {/* Filters */}
      <div className="flex gap-4">
        <Select
          value={params.featured === undefined ? 'all' : params.featured.toString()}
          onValueChange={handleFeaturedFilter}
        >
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Filter by featured" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            <SelectItem value="true">Featured Only</SelectItem>
            <SelectItem value="false">Not Featured</SelectItem>
          </SelectContent>
        </Select>

        <Select
          value={
            params.parent_id === undefined
              ? 'all'
              : params.parent_id === null
                ? 'none'
                : params.parent_id
          }
          onValueChange={handleParentFilter}
        >
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Filter by parent" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            <SelectItem value="none">Top Level Only</SelectItem>
            {parentCategories.map((category) => (
              <SelectItem key={category.id} value={category.id}>
                {category.name} (subcategories)
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <DataTable
        columns={columns}
        data={categories}
        totalCount={totalCount}
        pageSize={params.per_page || 10}
        currentPage={params.page || 1}
        onPageChange={handlePageChange}
        onSearch={handleSearch}
        searchPlaceholder="Search categories..."
        isLoading={loading}
      />
    </div>
  );
}
