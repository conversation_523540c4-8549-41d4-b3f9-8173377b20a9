"use client";
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { AdminHeader } from '@/components/admin';
import { CreateProductParams, Category, AdminStore } from '@/features/admin/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Loader2, AlertCircle } from 'lucide-react';
import { slugify } from '@/lib/utils/string';
import { useAuth } from '@/lib/hooks/useAuth';
import { getCurrentUserStore } from '@/features/admin/api';

export default function NewProductPage() {
  const router = useRouter();
  const { role } = useAuth();
  const [categories, setCategories] = useState<Category[]>([]);
  const [stores, setStores] = useState<AdminStore[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<CreateProductParams>({
    name: '',
    slug: '',
    description: '',
    price: 0,
    compare_at_price: 0,
    currency: 'GMD',
    category_id: null,
    store_id: '',
    featured: false,
    trending: false,
    in_stock: true,
  });

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Check if we have a store parameter in the URL
        const searchParams = new URLSearchParams(window.location.search);
        const storeParam = searchParams.get('store');

        // Fetch categories and stores separately to handle errors individually
        try {
          // Use the admin API endpoint instead of the client-side function
          const response = await fetch('/api/admin/categories');
          const data = await response.json();

          if (response.ok) {
            setCategories(data);
          } else {
            throw new Error(data.error || 'Failed to fetch categories');
          }
        } catch (categoryError) {
          console.error('Error fetching categories:', categoryError);
          // Don't fail completely, just show empty categories
          setCategories([]);
        }

        try {
          // For store owners, get only their stores
          if (role === 'store_owner') {
            const { store, error } = await getCurrentUserStore();
            if (error) {
              throw new Error(error);
            }
            if (store) {
              setStores([store]);
              // Auto-select the store owner's store
              setFormData(prev => ({ ...prev, store_id: store.id }));
            } else {
              setStores([]);
            }
          } else {
            // For admins, get all stores
            const response = await fetch('/api/admin/stores');
            const data = await response.json();

            if (data.error) {
              throw new Error(data.error);
            }

            if (data.stores.length === 0) {
            // If no stores exist, try to create a default Synergy store
            try {
              console.log('No stores found, attempting to create Synergy store');

              // First, get a user to be the owner
              const userResponse = await fetch('/api/admin/users');
              const userData = await userResponse.json();

              if (userData.error) {
                throw new Error(userData.error);
              }

              if (userData.users.length === 0) {
                throw new Error('No users available to be store owner');
              }

              // Use the first user as the store owner
              const ownerId = userData.users[0].id;

              // Create the Synergy store
              const storeResponse = await fetch('/api/admin/stores', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  name: 'Synergy Store',
                  slug: 'synergy',
                  description: 'The official Synergy marketplace store',
                  owner_id: ownerId,
                  status: 'active',
                  featured: true
                }),
              });

              if (!storeResponse.ok) {
                const errorData = await storeResponse.json();
                throw new Error(errorData.error || 'Failed to create Synergy store');
              }

              const synergyStore = await storeResponse.json();

              if (synergyStore) {
                setStores([synergyStore]);
                // Auto-select the Synergy store
                setFormData(prev => ({ ...prev, store_id: synergyStore.id }));
              } else {
                setStores([]);
              }
            } catch (createStoreError) {
              console.error('Error creating Synergy store:', createStoreError);
              setStores([]);
            }
            } else {
              setStores(data.stores);

              // If we have a store parameter and it's "synergy", find the Synergy store and select it
              if (storeParam === 'synergy') {
                const synergyStore = data.stores.find((store: AdminStore) =>
                  store.slug === 'synergy' || store.name.toLowerCase().includes('synergy')
                );

                if (synergyStore) {
                  setFormData(prev => ({ ...prev, store_id: synergyStore.id }));
                }
              }
            }
          }
        } catch (storeError) {
          console.error('Error fetching stores:', storeError);
          // Don't fail completely, just show empty stores
          setStores([]);
        }
      } catch (error) {
        console.error('Error in fetchData:', error);
        setError('Failed to load data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();

    // Clean up preview URL when component unmounts
    return () => {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl, role]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Auto-generate slug when name changes
    if (name === 'name') {
      setFormData((prev) => ({ ...prev, slug: slugify(value) }));
    }
  };

  const handleNumberChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: parseFloat(value) || 0 }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData((prev) => ({ ...prev, [name]: checked }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setSelectedFile(file);

      // Create a preview URL for the selected file
      const fileUrl = URL.createObjectURL(file);
      setPreviewUrl(fileUrl);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    setError(null);

    // Handle the "none" value for category_id
    const submissionData = {
      ...formData,
      category_id: formData.category_id === 'none' ? null : formData.category_id
    };

    try {
      // Use the admin API endpoint instead of the client-side function
      const response = await fetch('/api/admin/products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submissionData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create product');
      }

      const newProduct = await response.json();

      // If we have a selected file, upload it as a product image
      if (newProduct && selectedFile) {
        const imageFormData = new FormData();
        imageFormData.append('product_id', newProduct.id);
        imageFormData.append('file', selectedFile);
        imageFormData.append('alt', formData.name);
        imageFormData.append('position', '0');

        const imageResponse = await fetch('/api/admin/product-images', {
          method: 'POST',
          body: imageFormData,
        });

        if (!imageResponse.ok) {
          console.error('Error uploading product image');
          // Continue anyway, the product was created successfully
        }
      }

      if (newProduct) {
        router.push('/admin/products');
        router.refresh();
      } else {
        setError('Failed to create product');
      }
    } catch (error) {
      console.error('Error creating product:', error);
      setError('An error occurred while creating the product');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  // If no stores are available after loading, show a message
  if (!loading && stores.length === 0) {
    return (
      <div className="space-y-6">
        <AdminHeader
          title="Add New Product"
          description="Create a new product in your marketplace"
          backHref="/admin/products"
        />

        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>No Stores Available</AlertTitle>
          <AlertDescription>
            You need to create at least one store before you can add products.
            <div className="mt-2">
              <Button onClick={() => router.push('/admin/stores/new')}>
                Create a Store
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <AdminHeader
        title="Add New Product"
        description="Create a new product in your marketplace"
        backHref="/admin/products"
      />

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Product Information</CardTitle>
                <CardDescription>
                  Enter the product&apos;s basic information
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Product Name</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="slug">Product Slug</Label>
                  <Input
                    id="slug"
                    name="slug"
                    value={formData.slug}
                    onChange={handleChange}
                    required
                  />
                  <p className="text-sm text-gray-500">
                    This will be used in the product&apos;s URL: /products/{formData.slug}
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    rows={4}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="price">Price (GMD)</Label>
                    <Input
                      id="price"
                      name="price"
                      type="number"
                      step="0.01"
                      min="0"
                      value={formData.price}
                      onChange={handleNumberChange}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="compare_at_price">Compare at Price (GMD)</Label>
                    <Input
                      id="compare_at_price"
                      name="compare_at_price"
                      type="number"
                      step="0.01"
                      min="0"
                      value={formData.compare_at_price || ''}
                      onChange={handleNumberChange}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <Label htmlFor="category_id">Category</Label>
                      <Link href="/admin/categories/new">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                        >
                          Add New Category
                        </Button>
                      </Link>
                    </div>
                    <Select
                      value={formData.category_id || undefined}
                      onValueChange={(value) => handleSelectChange('category_id', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select a category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">None</SelectItem>
                        {categories.length > 0 ? (
                          categories.map((category) => (
                            <SelectItem key={category.id} value={category.id}>
                              {category.name}
                            </SelectItem>
                          ))
                        ) : (
                          <SelectItem value="no-categories" disabled>
                            No categories available
                          </SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="store_id">Store</Label>
                    <Select
                      value={formData.store_id || undefined}
                      onValueChange={(value) => handleSelectChange('store_id', value)}
                      required
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select a store" />
                      </SelectTrigger>
                      <SelectContent>
                        {stores.length > 0 ? (
                          stores.map((store) => (
                            <SelectItem key={store.id} value={store.id}>
                              {store.name}
                            </SelectItem>
                          ))
                        ) : (
                          <SelectItem value="no-stores" disabled>
                            No stores available
                          </SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Product Image</CardTitle>
                <CardDescription>
                  Add an image for your product
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="product-image">Product Image</Label>
                  <Input
                    id="product-image"
                    type="file"
                    accept="image/*"
                    onChange={handleFileChange}
                  />
                  <p className="text-sm text-gray-500">
                    Upload an image for your product (JPEG, PNG, WebP)
                  </p>
                </div>

                {previewUrl && (
                  <div className="mt-4 border rounded-md overflow-hidden">
                    <div className="aspect-square relative bg-gray-100">
                      <Image
                        src={previewUrl}
                        alt="Product preview"
                        fill
                        className="object-contain"
                      />
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Product Status</CardTitle>
                <CardDescription>
                  Set the product&apos;s visibility and status
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="in_stock"
                    checked={formData.in_stock}
                    onCheckedChange={(checked) => handleSwitchChange('in_stock', checked)}
                  />
                  <Label htmlFor="in_stock">In Stock</Label>
                </div>

                {/* Only show Featured and Trending options for admins */}
                {role === 'admin' && (
                  <>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="featured"
                        checked={formData.featured}
                        onCheckedChange={(checked) => handleSwitchChange('featured', checked)}
                      />
                      <Label htmlFor="featured">Featured Product</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        id="trending"
                        checked={formData.trending}
                        onCheckedChange={(checked) => handleSwitchChange('trending', checked)}
                      />
                      <Label htmlFor="trending">Trending Product</Label>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

        <div className="mt-6 flex justify-end gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push('/admin/products')}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={saving}>
            {saving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Create Product
          </Button>
        </div>
      </form>
    </div>
  );
}
