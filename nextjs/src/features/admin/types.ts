// Admin types

export type UserRole = 'user' | 'admin';

export type StoreStatus = 'active' | 'disabled' | 'suspended';

export interface AdminUser {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  phone?: string;
  role: UserRole;
  created_at: string;
  updated_at: string;
}

export interface AdminStore {
  id: string;
  name: string;
  slug: string;
  description?: string;
  logo?: string;
  cover_image?: string;
  owner_id: string;
  owner_email?: string;
  contact_email?: string;
  contact_phone?: string;
  address: string;
  status: StoreStatus;
  featured: boolean;
  rating: number;
  review_count: number;
  offers_delivery?: boolean;
  delivery_fee?: number;
  delivery_radius_km?: number;
  delivery_time_estimate?: string;
  created_at: string;
  updated_at: string;
}

export interface AdminProduct {
  id: string;
  name: string;
  slug: string;
  description?: string;
  price: number;
  compare_at_price?: number;
  currency: string;
  category_id?: string;
  category_name?: string;
  store_id: string;
  store_name?: string;
  featured: boolean;
  trending: boolean;
  in_stock: boolean;
  rating: number;
  review_count: number;
  created_at: string;
  updated_at: string;
}

export type OrderStatus = 'pending' | 'under_review' | 'awaiting_store_confirmation' | 'accepted_by_store' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded';

// Order status constants to avoid magic strings
export const ORDER_STATUS = {
  PENDING: 'pending' as const,
  UNDER_REVIEW: 'under_review' as const,
  AWAITING_STORE_CONFIRMATION: 'awaiting_store_confirmation' as const,
  ACCEPTED_BY_STORE: 'accepted_by_store' as const,
  PROCESSING: 'processing' as const,
  SHIPPED: 'shipped' as const,
  DELIVERED: 'delivered' as const,
  CANCELLED: 'cancelled' as const,
  REFUNDED: 'refunded' as const,
} as const;

export interface AdminOrder {
  id: string;
  user_id: string;
  user_email?: string;
  status: OrderStatus;
  total: number;
  currency: string;
  created_at: string;
  updated_at: string;
  items_count?: number;
  // Payment information
  payment_status?: string;
  payment_method?: string;
  transaction_id?: string;
  payments?: AdminPayment[];
  // Shipping information for store owners
  shipping_name?: string;
  shipping_email?: string;
  shipping_phone?: string;
  shipping_address?: string;
  shipping_city?: string;
  shipping_state?: string;
  shipping_country?: string;
  shipping_postal_code?: string;
  notes?: string;
}

export interface AdminPayment {
  id: string;
  order_id: string;
  amount: number;
  currency: string;
  payment_method: string;
  payment_status: string;
  transaction_id?: string;
  created_at: string;
  updated_at: string;
}

export interface AdminPayout {
  id: string;
  store_id: string;
  store_name?: string;
  order_id: string;
  amount: number;
  commission: number;
  currency: string;
  payout_status: string;
  transaction_id?: string;
  created_at: string;
  updated_at: string;
}

export interface DashboardStats {
  users_count: number;
  stores_count: number;
  products_count: number;
  orders_count: number;
  total_sales: number;
  finder_earnings: number;
  recent_orders: AdminOrder[];
  recent_users: AdminUser[];
}

export interface UserListParams {
  page?: number;
  per_page?: number;
  search?: string;
  role?: UserRole;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

export interface StoreListParams {
  page?: number;
  per_page?: number;
  search?: string;
  status?: StoreStatus;
  featured?: boolean;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

export interface ProductListParams {
  page?: number;
  per_page?: number;
  search?: string;
  category_id?: string;
  store_id?: string;
  featured?: boolean;
  trending?: boolean;
  in_stock?: boolean;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

export interface OrderListParams {
  page?: number;
  per_page?: number;
  search?: string;
  status?: string;
  user_id?: string;
  store_id?: string;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

export interface PaymentListParams {
  page?: number;
  per_page?: number;
  search?: string;
  payment_status?: string;
  payment_method?: string;
  order_id?: string;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

export interface PayoutListParams {
  page?: number;
  per_page?: number;
  search?: string;
  payout_status?: string;
  store_id?: string;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

export interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  image?: string;
  thumbnail?: string;
  parent_id?: string;
  featured: boolean;
  product_count?: number;
  created_at: string;
  updated_at: string;
}

export interface UpdateUserParams {
  first_name?: string;
  last_name?: string;
  email?: string;
  phone?: string;
  role?: UserRole;
}

export interface UpdateStoreParams {
  name?: string;
  description?: string;
  address: string;
  logo?: string;
  cover_image?: string;
  contact_email?: string;
  contact_phone?: string;
  status?: StoreStatus;
  featured?: boolean;
  offers_delivery?: boolean;
  delivery_fee?: number;
  delivery_radius_km?: number;
  delivery_time_estimate?: string;
}

export interface UpdateProductParams {
  name?: string;
  description?: string;
  price?: number;
  compare_at_price?: number;
  currency?: string;
  category_id?: string;
  featured?: boolean;
  trending?: boolean;
  in_stock?: boolean;
}

export interface CreateStoreParams {
  name: string;
  slug: string;
  description?: string;
  logo?: string;
  cover_image?: string;
  owner_id: string;
  contact_email?: string;
  contact_phone?: string;
  status?: StoreStatus;
  featured?: boolean;
  offers_delivery?: boolean;
  delivery_fee?: number;
  delivery_radius_km?: number;
  delivery_time_estimate?: string;
}

export interface UpdateOrderParams {
  status?: string;
  notes?: string;
}

export interface UpdatePaymentParams {
  payment_status?: string;
  transaction_id?: string;
  payment_details?: Record<string, any>;
}

export interface UpdatePayoutParams {
  payout_status?: string;
  transaction_id?: string;
  payout_details?: Record<string, any>;
}

export interface CreateProductParams {
  name: string;
  slug: string;
  description?: string;
  price: number;
  compare_at_price?: number;
  currency?: string;
  category_id?: string | null;
  store_id: string;
  featured?: boolean;
  trending?: boolean;
  in_stock?: boolean;
}

export interface CategoryListParams {
  page?: number;
  per_page?: number;
  search?: string;
  featured?: boolean;
  parent_id?: string;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

export interface CreateCategoryParams {
  name: string;
  slug: string;
  description?: string;
  image?: string;
  thumbnail?: string;
  parent_id?: string;
  featured?: boolean;
}

export interface UpdateCategoryParams {
  name?: string;
  slug?: string;
  description?: string;
  image?: string;
  thumbnail?: string;
  parent_id?: string;
  featured?: boolean;
}

// Deal types
export type DealType = 'flash' | 'clearance' | 'seasonal' | 'regular';

export interface AdminDeal {
  id: string;
  product_id: string;
  product_name?: string;
  product_slug?: string;
  store_id?: string;
  store_name?: string;
  title: string;
  description?: string;
  original_price: number;
  deal_price: number;
  discount_percentage: number;
  deal_type: DealType;
  start_date: string;
  end_date: string;
  is_active: boolean;
  featured: boolean;
  max_quantity?: number;
  used_quantity: number;
  currency: string;
  created_at: string;
  updated_at: string;
}

export interface DealListParams {
  page?: number;
  per_page?: number;
  search?: string;
  deal_type?: DealType;
  is_active?: boolean;
  featured?: boolean;
  product_id?: string;
  store_id?: string;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

export interface CreateDealParams {
  product_id: string;
  title: string;
  description?: string;
  original_price: number;
  deal_price: number;
  deal_type: DealType;
  start_date: string;
  end_date: string;
  is_active?: boolean;
  featured?: boolean;
  max_quantity?: number;
  currency?: string;
}

export interface UpdateDealParams {
  title?: string;
  description?: string;
  original_price?: number;
  deal_price?: number;
  deal_type?: DealType;
  start_date?: string;
  end_date?: string;
  is_active?: boolean;
  featured?: boolean;
  max_quantity?: number;
}

// External Delivery Service types
export interface ExternalDeliveryService {
  id: string;
  name: string;
  description?: string;
  logo?: string;
  contact_phone?: string;
  contact_email?: string;
  website?: string;
  base_fee: number;
  per_km_fee?: number;
  minimum_order_amount?: number;
  maximum_delivery_radius_km?: number;
  estimated_delivery_time?: string;
  is_active: boolean;
  coverage_areas?: string[];
  operating_hours?: Record<string, string>;
  created_at: string;
  updated_at: string;
}

export interface DeliveryServicePricing {
  id: string;
  service_id: string;
  area_name: string;
  base_fee: number;
  per_km_fee?: number;
  minimum_order_amount?: number;
  maximum_distance_km?: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateDeliveryServiceParams {
  name: string;
  description?: string;
  logo?: string;
  contact_phone?: string;
  contact_email?: string;
  website?: string;
  base_fee: number;
  per_km_fee?: number;
  minimum_order_amount?: number;
  maximum_delivery_radius_km?: number;
  estimated_delivery_time?: string;
  is_active?: boolean;
  coverage_areas?: string[];
  operating_hours?: Record<string, string>;
}

export interface UpdateDeliveryServiceParams {
  name?: string;
  description?: string;
  logo?: string;
  contact_phone?: string;
  contact_email?: string;
  website?: string;
  base_fee?: number;
  per_km_fee?: number;
  minimum_order_amount?: number;
  maximum_delivery_radius_km?: number;
  estimated_delivery_time?: string;
  is_active?: boolean;
  coverage_areas?: string[];
  operating_hours?: Record<string, string>;
}
