'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Heart, ShoppingCart, Share2, Star, Truck, Shield, ArrowLeft } from 'lucide-react';
import { Product } from '../types';
import { formatCurrency } from '@/lib/utils';
import { useToast } from '@/lib/hooks/use-toast';
import { useCart } from '@/lib/context/CartContext';

interface ProductDetailsProps {
  product: Product;
  className?: string;
}

export function ProductDetails({ product, className = '' }: ProductDetailsProps) {
  const { toast } = useToast();
  const { addToCart } = useCart();
  const [selectedImage, setSelectedImage] = useState(0);
  const [quantity, setQuantity] = useState(1);

  const {
    id,
    name,
    description,
    price,
    compareAtPrice,
    currency,
    images,
    category,
    store,
    rating,
    reviewCount,
    specifications,
    // Always treat products as in stock
    inStock = true,
  } = product;

  const handleAddToCart = async () => {
    try {
      // Use the addToCart method from the cart context
      await addToCart(id, quantity);
      toast({
        title: "Added to cart",
        description: `${quantity} ${quantity > 1 ? 'items' : 'item'} added to your cart.`,
        variant: "success",
      });
    } catch (error) {
      console.error('Error adding to cart:', error);
      toast({
        title: "Error",
        description: "Failed to add item to cart. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleAddToWishlist = async () => {
    try {
      // Import and use wishlist service
      const { createSPASassClient } = await import('@/lib/supabase/client');
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();

      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        toast({
          title: "Sign in required",
          description: "Please sign in to add items to your wishlist.",
          variant: "default",
        });
        return;
      }

      const { error } = await supabase
        .from('wishlist')
        .insert({
          user_id: user.id,
          product_id: id
        });

      if (error && error.code !== '23505') { // Ignore unique constraint violations
        throw error;
      }

      toast({
        title: "Added to wishlist",
        description: `${name} has been added to your wishlist.`,
        variant: "success",
      });
    } catch (error) {
      console.error('Error adding to wishlist:', error);
      toast({
        title: "Error",
        description: "Failed to add item to wishlist. Please try again.",
        variant: "destructive",
      });
    }
  };

  const discount = compareAtPrice ? Math.round(((compareAtPrice - price) / compareAtPrice) * 100) : 0;

  const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    if (value > 0) {
      setQuantity(value);
    }
  };

  const incrementQuantity = () => {
    setQuantity(quantity + 1);
  };

  const decrementQuantity = () => {
    if (quantity > 1) {
      setQuantity(quantity - 1);
    }
  };

  return (
    <div className={`bg-white ${className}`}>
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumbs */}
        <nav className="flex items-center text-sm mb-6">
          <Link href="/" className="text-gray-500 hover:text-primary-600">Home</Link>
          <span className="mx-2 text-gray-400">/</span>
          {category && (
            <>
              <Link href={`/categories/${category.slug}`} className="text-gray-500 hover:text-primary-600">
                {category.name}
              </Link>
              <span className="mx-2 text-gray-400">/</span>
            </>
          )}
          <span className="text-gray-900 font-medium">{name}</span>
        </nav>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Product Images */}
          <div>
            {/* Main image */}
            <div className="relative aspect-square rounded-lg overflow-hidden border border-gray-200 mb-4">
              <Image
                src={images && images[selectedImage]?.url || ('imageUrl' in product ? product.imageUrl as string : '/placeholder-product.jpg')}
                alt={name}
                fill
                sizes="(max-width: 1024px) 100vw, 50vw"
                className="object-cover object-center"
              />
              {discount > 0 && (
                <div className="absolute top-4 left-4 bg-red-500 text-white text-sm font-medium px-2 py-1 rounded">
                  {discount}% OFF
                </div>
              )}
            </div>

            {/* Thumbnail images */}
            {images && images.length > 1 && (
              <div className="grid grid-cols-5 gap-2">
                {images.map((image, index) => (
                  <button
                    key={index}
                    className={`relative aspect-square rounded border ${
                      selectedImage === index ? 'border-primary-600' : 'border-gray-200'
                    } overflow-hidden`}
                    onClick={() => setSelectedImage(index)}
                  >
                    <Image
                      src={image.url}
                      alt={`${name} - Image ${index + 1}`}
                      fill
                      sizes="20vw"
                      className="object-cover object-center"
                    />
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Product Info */}
          <div>
            {/* Store info if available */}
            {store && (
              <Link href={`/stores/${store.slug}`} className="inline-flex items-center text-sm text-gray-500 hover:text-primary-600 mb-2">
                <ArrowLeft className="w-4 h-4 mr-1" />
                More from {store.name}
              </Link>
            )}

            {/* Product name */}
            <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-2">{name}</h1>

            {/* Rating */}
            <div className="flex items-center mb-4">
              <div className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`w-4 h-4 ${
                      i < Math.floor(rating) ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'
                    }`}
                  />
                ))}
              </div>
              <span className="text-sm text-gray-500 ml-2">
                {rating.toFixed(1)} ({reviewCount} reviews)
              </span>
            </div>

            {/* Price */}
            <div className="mb-6">
              <div className="flex items-center">
                <span className="text-2xl font-bold text-gray-900">
                  {formatCurrency(price, currency)}
                </span>
                {compareAtPrice && compareAtPrice > price && (
                  <span className="text-lg text-gray-500 line-through ml-2">
                    {formatCurrency(compareAtPrice, currency)}
                  </span>
                )}
              </div>
              {inStock ? (
                <span className="text-sm text-green-600 mt-1 block">In Stock</span>
              ) : (
                <span className="text-sm text-red-600 mt-1 block">Out of Stock</span>
              )}
            </div>

            {/* Description */}
            <div className="mb-6">
              <p className="text-gray-700">{description}</p>
            </div>

            {/* Add to cart */}
            <div className="mb-6">
              <div className="flex items-center mb-4">
                <div className="flex items-center border border-gray-300 rounded-md mr-4">
                  <button
                    onClick={decrementQuantity}
                    className="px-3 py-2 text-gray-600 hover:text-gray-900"
                    disabled={quantity <= 1}
                  >
                    -
                  </button>
                  <input
                    type="number"
                    min="1"
                    value={quantity}
                    onChange={handleQuantityChange}
                    className="w-12 text-center border-0 focus:ring-0"
                  />
                  <button
                    onClick={incrementQuantity}
                    className="px-3 py-2 text-gray-600 hover:text-gray-900"
                  >
                    +
                  </button>
                </div>
                <button
                  className="flex-1 bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-md flex items-center justify-center"
                  onClick={handleAddToCart}
                >
                  <ShoppingCart className="w-4 h-4 mr-2" />
                  Add to Cart
                </button>
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={handleAddToWishlist}
                  className="flex items-center justify-center border border-gray-300 rounded-md py-2 px-4 text-gray-700 hover:bg-gray-50 hover:text-red-500 transition-colors"
                >
                  <Heart className="w-4 h-4 mr-2" />
                  Add to Wishlist
                </button>
                <button className="flex items-center justify-center border border-gray-300 rounded-md py-2 px-4 text-gray-700 hover:bg-gray-50">
                  <Share2 className="w-4 h-4 mr-2" />
                  Share
                </button>
              </div>
            </div>

            {/* Shipping & Returns */}
            <div className="border-t border-gray-200 pt-6 space-y-4">
              <div className="flex items-start">
                <Truck className="w-5 h-5 text-gray-400 mr-3 flex-shrink-0" />
                <div>
                  <h4 className="text-sm font-medium text-gray-900">Free Shipping</h4>
                  <p className="text-sm text-gray-500">Free standard shipping on orders over D1000</p>
                </div>
              </div>
              <div className="flex items-start">
                <Shield className="w-5 h-5 text-gray-400 mr-3 flex-shrink-0" />
                <div>
                  <h4 className="text-sm font-medium text-gray-900">Secure Payments</h4>
                  <p className="text-sm text-gray-500">Pay with Wave or Crypto</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Specifications */}
        {specifications && specifications.length > 0 && (
          <div className="mt-12">
            <h2 className="text-xl font-bold text-gray-900 mb-4">Specifications</h2>
            <div className="bg-gray-50 rounded-lg p-6">
              <dl className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4">
                {specifications.map((spec) => (
                  <div key={spec.id} className="flex">
                    <dt className="w-1/3 text-sm font-medium text-gray-500">{spec.name}</dt>
                    <dd className="w-2/3 text-sm text-gray-900">{spec.value}</dd>
                  </div>
                ))}
              </dl>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
